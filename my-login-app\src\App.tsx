import { useState, useEffect } from "react";
import { supabase } from "./lib/supabase";
import type { FormEvent } from "react";

function App() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [message, setMessage] = useState("");
  const [isSignUp, setIsSignUp] = useState(false);
  const [isResetPassword, setIsResetPassword] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState("");

  // Test Supabase connection on component mount
  useEffect(() => {
    async function testConnection() {
      try {
        const { error } = await supabase.auth.getSession();
        if (error) throw error;
        setConnectionStatus("Supabase connected successfully!");
      } catch (error) {
        setConnectionStatus(
          `Connection error: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      }
    }
    testConnection();
  }, []);

  // Handle Sign Up
  const handleSignUp = async (e: FormEvent) => {
    e.preventDefault();

    console.log("=== SIGNUP PROCESS STARTED ===");
    console.log("Form state:", {
      email: email,
      password: password ? "[HIDDEN]" : "empty",
      isSignUp: isSignUp,
    });

    try {
      // Always use email prefix as username (remove @ and everything after)
      const finalUsername = email.split("@")[0];

      console.log("Signup data:", {
        email,
        finalUsername,
        emailSplit: email.split("@")[0],
      });

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: finalUsername,
            display_name: finalUsername,
          },
        },
      });
      if (error) throw error;

      console.log("Supabase auth response:", data);
      console.log("User metadata:", data.user?.user_metadata);

      // Insert username into users table
      if (data.user) {
        console.log("Inserting user data:", {
          id: data.user.id,
          email,
          username: finalUsername,
        });

        // Check if user already exists in the table
        const { data: existingUser } = await supabase
          .from("users")
          .select("id")
          .eq("id", data.user.id)
          .single();

        if (!existingUser) {
          const { error: dbError } = await supabase
            .from("users")
            .insert([{ id: data.user.id, email, username: finalUsername }]);
          if (dbError) {
            console.error("Database error:", dbError);
            throw dbError;
          }
          console.log("User successfully inserted into database");
        } else {
          console.log("User already exists in database, skipping insert");
        }
      } else {
        console.warn("No user data returned from signup");
      }

      setMessage("Sign-up successful! Check your email to confirm.");

      // Verify user data after signup
      setTimeout(async () => {
        const { data: currentUser } = await supabase.auth.getUser();
        console.log("Current user after signup:", currentUser);
        console.log("Current user metadata:", currentUser.user?.user_metadata);
      }, 1000);
    } catch (error) {
      console.error("Signup error:", error);
      setMessage(
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  // Handle Login
  const handleLogin = async (e: FormEvent) => {
    e.preventDefault();
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (error) throw error;
      setMessage("Login successful!");
    } catch (error) {
      setMessage(
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  // Handle Password Reset
  const handlePasswordReset = async (e: FormEvent) => {
    e.preventDefault();
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: window.location.origin,
      });
      if (error) throw error;
      setMessage("Password reset email sent! Check your inbox.");
    } catch (error) {
      setMessage(
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  // Handle Form Submission
  const handleSubmit = (e: FormEvent) => {
    if (isResetPassword) return handlePasswordReset(e);
    if (isSignUp) return handleSignUp(e);
    return handleLogin(e);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
        <h2 className="text-2xl font-bold mb-6 text-center">
          {isResetPassword ? "Reset Password" : isSignUp ? "Sign Up" : "Login"}
        </h2>
        <p className="text-sm text-gray-600 mb-4">{connectionStatus}</p>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          {isSignUp && (
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Username (auto-generated from email)
              </label>
              <input
                type="text"
                value={email ? email.split("@")[0] : ""}
                readOnly
                className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-600"
                placeholder="Will be generated from email"
              />
            </div>
          )}
          {!isResetPassword && (
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
          )}
          <button
            type="submit"
            className="w-full bg-blue-500 text-white py-2 rounded-md hover:bg-blue-600 transition"
          >
            {isResetPassword
              ? "Send Reset Link"
              : isSignUp
              ? "Sign Up"
              : "Login"}
          </button>
        </form>
        {message && (
          <p className="mt-4 text-center text-sm text-red-500">{message}</p>
        )}
        <div className="mt-4 text-center text-sm">
          {!isResetPassword && !isSignUp && (
            <>
              <button
                onClick={() => setIsSignUp(true)}
                className="text-blue-500 hover:underline"
              >
                Need an account? Sign Up
              </button>
              <br />
              <button
                onClick={() => setIsResetPassword(true)}
                className="text-blue-500 hover:underline mt-2"
              >
                Forgot Password?
              </button>
            </>
          )}
          {(isSignUp || isResetPassword) && (
            <button
              onClick={() => {
                setIsSignUp(false);
                setIsResetPassword(false);
                setMessage("");
              }}
              className="text-blue-500 hover:underline"
            >
              Back to Login
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
