import { useState, useEffect } from "react";
import { supabase } from "./lib/supabase";
import type { FormEvent } from "react";

function App() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [message, setMessage] = useState("");
  const [isSignUp, setIsSignUp] = useState(false);
  const [isResetPassword, setIsResetPassword] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState("");

  // Test Supabase connection on component mount
  useEffect(() => {
    async function testConnection() {
      try {
        const { error } = await supabase.auth.getSession();
        if (error) throw error;
        setConnectionStatus("Supabase connected successfully!");
      } catch (error) {
        setConnectionStatus(
          `Connection error: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      }
    }
    testConnection();
  }, []);

  // Handle Sign Up
  const handleSignUp = async (e: FormEvent) => {
    e.preventDefault();

    console.log("=== SIGNUP PROCESS STARTED ===");
    console.log("Form state:", {
      email: email,
      password: password ? "[HIDDEN]" : "empty",
      isSignUp: isSignUp,
    });

    try {
      console.log("Signup data:", {
        email,
        username: "Will use email as username",
      });

      console.log("About to signup with email as username:", email);

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: email, // Email will be used as username
            full_name: email, // Use email as full_name initially
          },
        },
      });

      if (error) {
        console.error("Supabase auth signup error:", error);
        throw error;
      }

      console.log("Supabase auth response:", data);
      console.log("User metadata:", data.user?.user_metadata);
      console.log("Username in metadata:", data.user?.user_metadata?.username);

      // The profiles table will be automatically populated by the trigger
      // The trigger uses email as username
      if (data.user) {
        console.log("User created successfully with email as username:", email);
        console.log("Profile will be automatically created by trigger");

        // Fallback: manually create profile if trigger fails
        const userId = data.user.id;
        setTimeout(async () => {
          try {
            const { data: existingProfile } = await supabase
              .from("profiles")
              .select("id")
              .eq("id", userId)
              .single();

            if (!existingProfile) {
              console.log(
                "Trigger didn't create profile, creating manually..."
              );
              const { error: insertError } = await supabase
                .from("profiles")
                .insert({
                  id: userId,
                  username: email,
                  full_name: email,
                  updated_at: new Date().toISOString(),
                });

              if (insertError) {
                console.error("Manual profile creation failed:", insertError);
              } else {
                console.log("Profile created manually");
              }
            }
          } catch (err) {
            console.error("Error in fallback profile creation:", err);
          }
        }, 1500);
      } else {
        console.warn("No user data returned from signup");
      }

      setMessage("Sign-up successful! Check your email to confirm.");

      // Verify user data and profile creation after signup
      setTimeout(async () => {
        try {
          const { data: currentUser } = await supabase.auth.getUser();
          console.log("Current user after signup:", currentUser);
          console.log(
            "Current user metadata:",
            currentUser.user?.user_metadata
          );

          // Check if profile was created by trigger
          if (currentUser.user) {
            const { data: profile, error: profileError } = await supabase
              .from("profiles")
              .select("*")
              .eq("id", currentUser.user.id)
              .single();

            if (profileError) {
              console.error("Error fetching profile:", profileError);
              console.log("Profile may not have been created by trigger");
            } else {
              console.log("Profile created successfully:", profile);
            }
          }
        } catch (err) {
          console.error("Error in post-signup verification:", err);
        }
      }, 2000);
    } catch (error) {
      console.error("Signup error:", error);
      setMessage(
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  // Handle Login
  const handleLogin = async (e: FormEvent) => {
    e.preventDefault();
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (error) throw error;
      setMessage("Login successful!");
    } catch (error) {
      setMessage(
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  // Handle Password Reset
  const handlePasswordReset = async (e: FormEvent) => {
    e.preventDefault();
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: window.location.origin,
      });
      if (error) throw error;
      setMessage("Password reset email sent! Check your inbox.");
    } catch (error) {
      setMessage(
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  // Handle Form Submission
  const handleSubmit = (e: FormEvent) => {
    if (isResetPassword) return handlePasswordReset(e);
    if (isSignUp) return handleSignUp(e);
    return handleLogin(e);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
        <h2 className="text-2xl font-bold mb-6 text-center">
          {isResetPassword ? "Reset Password" : isSignUp ? "Sign Up" : "Login"}
        </h2>
        <p className="text-sm text-gray-600 mb-4">{connectionStatus}</p>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          {isSignUp && (
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Username (will use your email)
              </label>
              <input
                type="text"
                value={email}
                readOnly
                className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-600"
                placeholder="Your email will be used as username"
              />
            </div>
          )}
          {!isResetPassword && (
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
          )}
          <button
            type="submit"
            className="w-full bg-blue-500 text-white py-2 rounded-md hover:bg-blue-600 transition"
          >
            {isResetPassword
              ? "Send Reset Link"
              : isSignUp
              ? "Sign Up"
              : "Login"}
          </button>
        </form>
        {message && (
          <p className="mt-4 text-center text-sm text-red-500">{message}</p>
        )}
        <div className="mt-4 text-center text-sm">
          {!isResetPassword && !isSignUp && (
            <>
              <button
                onClick={() => setIsSignUp(true)}
                className="text-blue-500 hover:underline"
              >
                Need an account? Sign Up
              </button>
              <br />
              <button
                onClick={() => setIsResetPassword(true)}
                className="text-blue-500 hover:underline mt-2"
              >
                Forgot Password?
              </button>
            </>
          )}
          {(isSignUp || isResetPassword) && (
            <button
              onClick={() => {
                setIsSignUp(false);
                setIsResetPassword(false);
                setMessage("");
              }}
              className="text-blue-500 hover:underline"
            >
              Back to Login
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
