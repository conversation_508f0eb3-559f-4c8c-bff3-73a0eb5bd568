import { useState, useEffect } from "react";
import { supabase } from "./lib/supabase";
import type { FormEvent } from "react";

function App() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [message, setMessage] = useState("");
  const [isSignUp, setIsSignUp] = useState(false);
  const [isResetPassword, setIsResetPassword] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState("");

  // Test Supabase connection on component mount
  useEffect(() => {
    async function testConnection() {
      try {
        const { error } = await supabase.auth.getSession();
        if (error) throw error;
        setConnectionStatus("Supabase connected successfully!");
      } catch (error) {
        setConnectionStatus(
          `Connection error: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      }
    }
    testConnection();
  }, []);

  // Handle Sign Up
  const handleSignUp = async (e: FormEvent) => {
    e.preventDefault();

    console.log("=== SIGNUP PROCESS STARTED ===");
    console.log("Form state:", {
      email: email,
      password: password ? "[HIDDEN]" : "empty",
      isSignUp: isSignUp,
    });

    try {
      // Always use email prefix as username (remove @ and everything after)
      const username = email.split("@")[0];

      console.log("Signup data:", {
        email,
        username,
        emailSplit: email.split("@")[0],
      });

      console.log("About to signup with username:", username);

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: username,
            full_name: username, // Use username as full_name initially
          },
        },
      });
      if (error) throw error;

      console.log("Supabase auth response:", data);
      console.log("User metadata:", data.user?.user_metadata);
      console.log("Username in metadata:", data.user?.user_metadata?.username);

      // The profiles table will be automatically populated by the trigger
      // But we need to update it with the username since the trigger doesn't include it
      if (data.user) {
        console.log("User created, updating profile with username:", username);

        // Wait a moment for the trigger to create the profile
        const userId = data.user.id;
        setTimeout(async () => {
          try {
            const { error: updateError } = await supabase
              .from("profiles")
              .update({
                username: username,
                updated_at: new Date().toISOString(),
              })
              .eq("id", userId);

            if (updateError) {
              console.error("Error updating profile:", updateError);
            } else {
              console.log("Profile updated with username successfully");
            }
          } catch (err) {
            console.error("Error in profile update:", err);
          }
        }, 1000);
      } else {
        console.warn("No user data returned from signup");
      }

      setMessage("Sign-up successful! Check your email to confirm.");

      // Verify user data after signup
      setTimeout(async () => {
        const { data: currentUser } = await supabase.auth.getUser();
        console.log("Current user after signup:", currentUser);
        console.log("Current user metadata:", currentUser.user?.user_metadata);
      }, 1000);
    } catch (error) {
      console.error("Signup error:", error);
      setMessage(
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  // Handle Login
  const handleLogin = async (e: FormEvent) => {
    e.preventDefault();
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (error) throw error;
      setMessage("Login successful!");
    } catch (error) {
      setMessage(
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  // Handle Password Reset
  const handlePasswordReset = async (e: FormEvent) => {
    e.preventDefault();
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: window.location.origin,
      });
      if (error) throw error;
      setMessage("Password reset email sent! Check your inbox.");
    } catch (error) {
      setMessage(
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  // Handle Form Submission
  const handleSubmit = (e: FormEvent) => {
    if (isResetPassword) return handlePasswordReset(e);
    if (isSignUp) return handleSignUp(e);
    return handleLogin(e);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
        <h2 className="text-2xl font-bold mb-6 text-center">
          {isResetPassword ? "Reset Password" : isSignUp ? "Sign Up" : "Login"}
        </h2>
        <p className="text-sm text-gray-600 mb-4">{connectionStatus}</p>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          {isSignUp && (
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Username (auto-generated from email)
              </label>
              <input
                type="text"
                value={email ? email.split("@")[0] : ""}
                readOnly
                className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-600"
                placeholder="Will be generated from email"
              />
            </div>
          )}
          {!isResetPassword && (
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
          )}
          <button
            type="submit"
            className="w-full bg-blue-500 text-white py-2 rounded-md hover:bg-blue-600 transition"
          >
            {isResetPassword
              ? "Send Reset Link"
              : isSignUp
              ? "Sign Up"
              : "Login"}
          </button>
        </form>
        {message && (
          <p className="mt-4 text-center text-sm text-red-500">{message}</p>
        )}
        <div className="mt-4 text-center text-sm">
          {!isResetPassword && !isSignUp && (
            <>
              <button
                onClick={() => setIsSignUp(true)}
                className="text-blue-500 hover:underline"
              >
                Need an account? Sign Up
              </button>
              <br />
              <button
                onClick={() => setIsResetPassword(true)}
                className="text-blue-500 hover:underline mt-2"
              >
                Forgot Password?
              </button>
            </>
          )}
          {(isSignUp || isResetPassword) && (
            <button
              onClick={() => {
                setIsSignUp(false);
                setIsResetPassword(false);
                setMessage("");
              }}
              className="text-blue-500 hover:underline"
            >
              Back to Login
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
