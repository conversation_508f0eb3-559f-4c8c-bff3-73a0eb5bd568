import { useState, useEffect } from "react";
import { supabase } from "./lib/supabase";
import type { FormEvent } from "react";

function App() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [username, setUsername] = useState("");
  const [message, setMessage] = useState("");
  const [isSignUp, setIsSignUp] = useState(false);
  const [isResetPassword, setIsResetPassword] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState("");
  const [user, setUser] = useState<any>(null);
  const [profile, setProfile] = useState<any>(null);

  // Test Supabase connection and check auth state
  useEffect(() => {
    async function initializeAuth() {
      try {
        // Test connection
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) throw error;
        setConnectionStatus("Supabase connected successfully!");
        
        // Set initial user state
        if (session?.user) {
          setUser(session.user);
          await fetchUserProfile(session.user.id);
        }
      } catch (error) {
        setConnectionStatus(
          `Connection error: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      }
    }

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log("Auth state changed:", event, session);
        if (session?.user) {
          setUser(session.user);
          await fetchUserProfile(session.user.id);
        } else {
          setUser(null);
          setProfile(null);
        }
      }
    );

    initializeAuth();

    return () => subscription.unsubscribe();
  }, []);

  // Fetch user profile from profiles table
  const fetchUserProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (error) {
        console.error("Error fetching profile:", error);
      } else {
        setProfile(data);
        console.log("User profile:", data);
      }
    } catch (err) {
      console.error("Error in fetchUserProfile:", err);
    }
  };

  // Handle Sign Up
  const handleSignUp = async (e: FormEvent) => {
    e.preventDefault();

    console.log("=== SIGNUP PROCESS STARTED ===");
    console.log("Form state:", {
      email: email,
      username: username,
      password: password ? "[HIDDEN]" : "empty",
    });

    try {
      // Validate username
      if (!username.trim()) {
        throw new Error("Username is required");
      }
      if (username.trim().length < 3) {
        throw new Error("Username must be at least 3 characters long");
      }

      console.log("Signing up with:", {
        email,
        username: username.trim(),
      });

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: username.trim(),
            full_name: username.trim(),
          },
        },
      });

      if (error) {
        console.error("Supabase auth signup error:", error);
        throw error;
      }

      console.log("Signup successful:", data);
      setMessage("Sign-up successful! Check your email to confirm your account.");
      
      // Clear form
      setEmail("");
      setPassword("");
      setUsername("");
      
    } catch (error) {
      console.error("Signup error:", error);
      setMessage(
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  // Handle Login
  const handleLogin = async (e: FormEvent) => {
    e.preventDefault();
    
    console.log("=== LOGIN PROCESS STARTED ===");
    
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) throw error;
      
      console.log("Login successful:", data);
      setMessage("Login successful!");
      
      // Clear form
      setEmail("");
      setPassword("");
      
    } catch (error) {
      console.error("Login error:", error);
      setMessage(
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  // Handle Password Reset
  const handlePasswordReset = async (e: FormEvent) => {
    e.preventDefault();
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: window.location.origin,
      });
      if (error) throw error;
      setMessage("Password reset email sent! Check your inbox.");
    } catch (error) {
      setMessage(
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  // Handle Logout
  const handleLogout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      setMessage("Logged out successfully!");
    } catch (error) {
      setMessage(
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  // Handle Form Submission
  const handleSubmit = (e: FormEvent) => {
    if (isResetPassword) return handlePasswordReset(e);
    if (isSignUp) return handleSignUp(e);
    return handleLogin(e);
  };

  // If user is logged in, show dashboard
  if (user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
          <h2 className="text-2xl font-bold mb-6 text-center text-green-600">
            Welcome!
          </h2>
          
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-md">
              <h3 className="font-semibold text-gray-700 mb-2">User Info:</h3>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>User ID:</strong> {user.id}</p>
              <p><strong>Email Confirmed:</strong> {user.email_confirmed_at ? "Yes" : "No"}</p>
            </div>
            
            {profile && (
              <div className="bg-blue-50 p-4 rounded-md">
                <h3 className="font-semibold text-gray-700 mb-2">Profile Info:</h3>
                <p><strong>Username:</strong> {profile.username}</p>
                <p><strong>Full Name:</strong> {profile.full_name}</p>
                <p><strong>Created:</strong> {new Date(profile.updated_at).toLocaleDateString()}</p>
              </div>
            )}
          </div>
          
          <button
            onClick={handleLogout}
            className="w-full mt-6 bg-red-500 text-white py-2 rounded-md hover:bg-red-600 transition"
          >
            Logout
          </button>
          
          {message && (
            <p className="mt-4 text-center text-sm text-green-600">{message}</p>
          )}
        </div>
      </div>
    );
  }

  // Login/Signup form
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
        <h2 className="text-2xl font-bold mb-6 text-center">
          {isResetPassword ? "Reset Password" : isSignUp ? "Sign Up" : "Login"}
        </h2>
        
        <p className="text-sm text-gray-600 mb-4">{connectionStatus}</p>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          
          {isSignUp && (
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Username
              </label>
              <input
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your username (min 3 characters)"
                required
                minLength={3}
              />
            </div>
          )}
          
          {!isResetPassword && (
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
          )}
          
          <button
            type="submit"
            className="w-full bg-blue-500 text-white py-2 rounded-md hover:bg-blue-600 transition"
          >
            {isResetPassword
              ? "Send Reset Link"
              : isSignUp
              ? "Sign Up"
              : "Login"}
          </button>
        </form>
        
        {message && (
          <p className="mt-4 text-center text-sm text-red-500">{message}</p>
        )}
        
        <div className="mt-4 text-center text-sm">
          {!isResetPassword && !isSignUp && (
            <>
              <button
                onClick={() => {
                  setIsSignUp(true);
                  setMessage("");
                }}
                className="text-blue-500 hover:underline"
              >
                Need an account? Sign Up
              </button>
              <br />
              <button
                onClick={() => {
                  setIsResetPassword(true);
                  setMessage("");
                }}
                className="text-blue-500 hover:underline mt-2"
              >
                Forgot Password?
              </button>
            </>
          )}
          {(isSignUp || isResetPassword) && (
            <button
              onClick={() => {
                setIsSignUp(false);
                setIsResetPassword(false);
                setMessage("");
              }}
              className="text-blue-500 hover:underline"
            >
              Back to Login
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
